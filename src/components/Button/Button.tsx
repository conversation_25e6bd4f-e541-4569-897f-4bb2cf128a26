import { LinearGradient } from '@mrn/react-native-linear-gradient';
import React from 'react';
import { Text, TouchableOpacity } from 'react-native';

const Button = ({ onPress, buttonText, type = 'primary', style = {} }) => {
    return (
        <TouchableOpacity
            onPress={onPress}
            style={[
                {
                    height: 42,
                    borderRadius: 21,
                    overflow: 'hidden',
                    width: '100%',
                },
                style,
            ]}
        >
            <LinearGradient
                colors={['#4021FF', '#752FFF']}
                style={{
                    height: 42,
                    borderRadius: 21,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flex: 1,
                }}
            >
                <Text
                    style={{
                        fontSize: 14,
                        color: '#FFF',
                        fontWeight: '500',
                    }}
                >
                    {buttonText}
                </Text>
            </LinearGradient>
        </TouchableOpacity>
    );
};
export default Button;
