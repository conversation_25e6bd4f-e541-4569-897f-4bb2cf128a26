import { View, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useEffect, useState } from 'react';

import { announcementMarkdownStyles } from './announcementMarkdownStyle';
import useAnnouncement from './hooks/useAnnouncement';

import { MemoizedMarkdownInner } from '@/components/MessageBox/Answer/AnswerContent/MemoizedMarkdownInner';
import useMessage from '@/hooks/useMessage';

interface AnnouncementProps {
    style?: any;
    onClose?: () => void;
}

const Announcement: React.FC<AnnouncementProps> = ({ style, onClose }) => {
    const { announcement, loading, closeAnnouncement } = useAnnouncement();
    const [expanded, setExpanded] = useState(false);
    const [isVisible, setIsVisible] = useState(!!announcement?.content);
    const withFile = useMessage((state) => state.isWithFile);

    useEffect(() => {
        if (announcement?.content) {
            setIsVisible(true);
        }
    }, [announcement?.content]);

    const toggleExpanded = () => {
        setExpanded(!expanded);
    };

    const handleClose = async () => {
        if (announcement?.announcementId) {
            await closeAnnouncement(announcement?.announcementId);
            onClose?.();
            setIsVisible(false);
        }
    };

    if (loading || !isVisible || withFile()) {
        return null;
    }

    return (
        <View
            style={[
                styles.container,
                {
                    height: expanded ? undefined : 40,
                },
                style,
            ]}
        >
            <View style={styles.content}>
                <View style={styles.leftContent}>
                    <Icon
                        type="announcement-o"
                        size={16}
                        tintColor="#fff"
                        style={styles.icon}
                    />
                    <View
                        style={[
                            styles.textContainer,
                            {
                                maxHeight: expanded ? undefined : 18,
                                overflow: 'hidden',
                            },
                        ]}
                    >
                        <MemoizedMarkdownInner
                            style={announcementMarkdownStyles as any}
                        >
                            {announcement?.content}
                        </MemoizedMarkdownInner>
                    </View>
                </View>

                <View style={styles.rightContent}>
                    <TouchableOpacity
                        onPress={toggleExpanded}
                        style={styles.expandButton}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                        <Icon
                            type={'fast-backward'}
                            style={{
                                transform: [
                                    { rotate: expanded ? '90deg' : '-90deg' },
                                ],
                            }}
                            size={12}
                            tintColor="#FFF"
                        />
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={handleClose}
                        style={styles.closeButton}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                        <Icon type="close" size={14} tintColor="#eee" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderRadius: 8,
        overflow: 'hidden',
    },
    content: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    leftContent: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        flex: 1,
        paddingRight: 8,
    },
    icon: {
        marginRight: 6,
        marginTop: 2,
    },
    textContainer: {
        flex: 1,
        marginTop: -1,
    },
    text: {
        color: '#FFF',
        fontSize: 12,
        lineHeight: 18,
        flex: 1,
    },
    rightContent: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        height: '100%',
    },
    expandButton: {
        padding: 4,
        marginRight: 8,
    },
    closeButton: {
        padding: 4,
    },
});

export default Announcement;
