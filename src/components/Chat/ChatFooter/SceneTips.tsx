import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    Keyboard,
    View,
} from '@mrn/react-native';
import React, { useMemo } from 'react';

import useSceneTips from '../../AbilitySelector/useSceneTips';
import Condition from '../../Condition/Condition';
import RNImage from '../../RNImage';

import NetImages from '@/assets/images/homeRefactor';
import useMessage from '@/hooks/useMessage';
import { useSendMessage } from '@/hooks/useSendMessage';
import { useUiState } from '@/store/uiState';
import { EntryPointType } from '@/types';
import { getAdditionMessage } from '@/utils/message/getAdditionMessage';

interface SceneTipsProps {
    /** 是否显示组件 */
    visible?: boolean;
    /** 自定义容器样式 */
    containerStyle?: any;
    /** 自定义项目样式 */
    itemStyle?: any;
}

/**
 * 场景提示组件
 * 从 AbilitySelectorNew 中提取的 SceneTips 相关逻辑
 * 用于在有文件时显示推荐问题
 */
const SceneTips: React.FC<SceneTipsProps> = ({
    visible = true,
    containerStyle,
    itemStyle,
}) => {
    const { sceneTips } = useSceneTips();
    const { send } = useSendMessage();
    const { inputPosition } = useUiState();

    const file = useMessage((state) => state.file);
    const clearFile = useMessage((state) => state.clearFile);
    const isWithFile = useMessage((state) => state.isWithFile);

    // 获取推荐问题
    const recommendedQuestions = useMemo(() => {
        return sceneTips?.picture?.recommendedQuestions || [];
    }, [sceneTips]);

    // 计算绝对定位
    const absolutePosition = useMemo(() => {
        const bottom = inputPosition.height; // 键盘关闭时在输入框上方10像素
        return { bottom: bottom - 10 };
    }, [inputPosition.height]);

    // 渲染场景提示项
    const renderSceneTipsItem = (item: string, index: number) => {
        return (
            <TouchableOpacity
                key={item}
                style={[
                    styles.item,
                    {
                        marginRight:
                            index === recommendedQuestions.length - 1 ? 0 : 10,
                    },
                    isWithFile() ? { backgroundColor: '#f5f6fa' } : {},
                    itemStyle,
                ]}
                onPress={() => {
                    Keyboard.dismiss();
                    send(
                        getAdditionMessage(file, item),
                        EntryPointType.TOOL,
                        'picture_tip',
                    );
                    clearFile();
                }}
            >
                <Text style={styles.itemText}>{item}</Text>
                <RNImage
                    source={NetImages.arrowRight}
                    style={styles.arrowIcon}
                />
            </TouchableOpacity>
        );
    };

    // 如果没有文件或没有推荐问题，不显示
    if (!visible || file.length === 0 || recommendedQuestions.length === 0) {
        return null;
    }

    return (
        <Condition condition={[file.length > 0]}>
            <View
                style={[
                    styles.container,
                    {
                        left: 0,
                        right: 0,
                        zIndex: 999,
                    },
                    containerStyle,
                ]}
            >
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    style={styles.scrollView}
                >
                    {recommendedQuestions.map(renderSceneTipsItem)}
                </ScrollView>
            </View>
        </Condition>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        backgroundColor: 'transparent',
    },
    scrollView: {
        overflow: 'visible',
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
        borderRadius: 20,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    itemText: {
        fontSize: 12,
        color: '#333333',
        lineHeight: 16,
    },
    arrowIcon: {
        marginLeft: 4,
        width: 10,
        height: 10,
    },
});

export default SceneTips;
