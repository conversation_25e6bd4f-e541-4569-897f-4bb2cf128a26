import {
    StyleSheet,
    TouchableOpacity,
    View,
    Image,
    Text,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import { useDebounceFn } from 'ahooks';
import React, { useContext } from 'react';

import RootTagContext from '../../../hooks/rootTagContext';
import { useRefreshMessage } from '../hooks';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import RNImage from '@/components/RNImage';
import useMessage from '@/hooks/useMessage';
import { useUiState } from '@/store/uiState';
import TWS from '@/TWS';
import { trackButtonClick } from '@/utils/track';

interface ChatHeader {
    navigator: { pop: () => void };
    onTabChange?: (tab: TabType) => void;
    currentStep?: number;
    wrapWithTooltip?: (
        key: string,
        children: React.ReactNode,
    ) => React.ReactNode;
}

// Tab类型枚举
export enum TabType {
    DIALOGUE = 'dialogue',
    TASK = 'task',
}

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginTop: 10,
        paddingHorizontal: 12,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 44,
    },
    backButton: {
        padding: 4,
        marginRight: 8,
    },
    headerButton: {
        padding: 6,
        marginLeft: 8,
    },
    // Tab相关样式 - 纯文字样式，位于header中间
    tabContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
    },
    tabItem: {
        paddingHorizontal: 12,
        paddingVertical: 8,
        marginHorizontal: 8,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 6,
    },
    tabText: {
        fontSize: 16,
        color: '#666',
    },
    activeTabText: {
        color: '#222',
        fontSize: 18,
        fontWeight: '600',
    },
    // 保留的必要样式（如果需要）
    closeCircle: {
        height: 20,
        width: 20,
        borderRadius: 10,
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const ChatHeader: React.FC<ChatHeader> = ({
    onTabChange,
    wrapWithTooltip,
    currentStep,
}) => {
    const { pop } = useContext(RootTagContext);
    const activeTab = useMessage((state) => state.activeTab);
    const setActiveTab = useMessage((state) => state.setActiveTab);
    const reset = useMessage((state) => state.reset);
    const { run: onClose } = useDebounceFn(
        () => {
            pop();
        },
        { wait: 200 },
    );

    const showHome = useUiState((state) => state.showHome);
    const setShowHome = useUiState((state) => state.setShowHome);
    const refreshMessage = useRefreshMessage();
    const getHistoryMessageList = useMessage(
        (state) => state.getHistoryMessageList,
    );

    const handleTabPress = (tab: TabType) => {
        setActiveTab(tab);
        trackButtonClick(`tab_${tab}`);

        // 通知父组件tab变化
        onTabChange?.(tab);
    };

    return (
        <View style={[styles.container]}>
            {/* 左侧 */}
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity onPress={onClose} style={styles.backButton}>
                    <Icon type="left" size={18} />
                </TouchableOpacity>
                <Condition condition={[!showHome]}>
                    <Image
                        source={{
                            uri: NetImages.miniIcon,
                        }}
                        style={[TWS.square(32)]}
                    />
                </Condition>
            </View>

            {/* 中间Tab */}
            <View style={styles.tabContainer}>
                <TouchableOpacity
                    style={styles.tabItem}
                    onPress={() => handleTabPress(TabType.DIALOGUE)}
                >
                    <Text
                        style={[
                            styles.tabText,
                            activeTab === TabType.DIALOGUE &&
                                styles.activeTabText,
                        ]}
                    >
                        对话
                    </Text>
                </TouchableOpacity>
                {wrapWithTooltip(
                    'taskTab',
                    <TouchableOpacity
                        style={[
                            styles.tabItem,
                            currentStep === 3
                                ? { backgroundColor: '#fff' }
                                : {},
                        ]}
                        onPress={() => handleTabPress(TabType.TASK)}
                    >
                        <Text
                            style={[
                                styles.tabText,
                                activeTab === TabType.TASK &&
                                    styles.activeTabText,
                            ]}
                        >
                            任务
                        </Text>
                    </TouchableOpacity>,
                )}
            </View>

            {/* 右侧按钮 - 任务场景下隐藏但保持占位 */}
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Condition condition={[!showHome, showHome]}>
                    <TouchableOpacity
                        onPress={() => {
                            // 清空所有会话内容
                            reset();
                            refreshMessage(true);
                            setShowHome(true);
                        }}
                        style={[
                            styles.headerButton,
                            { opacity: activeTab === TabType.TASK ? 0 : 1 },
                        ]}
                        disabled={activeTab === TabType.TASK}
                    >
                        <RNImage
                            source={NetImages.refreshIcon}
                            style={{ width: 20 }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            setShowHome(false);
                            getHistoryMessageList();
                            trackButtonClick('history');
                        }}
                        style={[
                            styles.headerButton,
                            { opacity: activeTab === TabType.TASK ? 0 : 1 },
                        ]}
                        disabled={activeTab === TabType.TASK}
                    >
                        <RNImage
                            source={NetImages.historyIcon}
                            style={{ width: 20 }}
                        />
                    </TouchableOpacity>
                </Condition>
            </View>
        </View>
    );
};

export default ChatHeader;
