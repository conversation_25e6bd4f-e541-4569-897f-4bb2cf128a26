import { View, StyleSheet } from '@mrn/react-native';
import React from 'react';

import { COLORS } from '@/consts';

interface PaginationIndicatorProps {
    /** 总页数 */
    totalPages: number;
    /** 当前页索引 */
    currentPage: number;
    /** 指示器样式类型 */
    variant?: 'default' | 'card' | 'small';
    /** 自定义容器样式 */
    containerStyle?: any;
    /** 自定义点样式 */
    dotStyle?: any;
    /** 自定义激活点样式 */
    activeDotStyle?: any;
}

/**
 * 分页指示器组件
 * 提取自 SkillCards 组件，可复用于其他需要分页指示的场景
 */
const PaginationIndicator: React.FC<PaginationIndicatorProps> = ({
    totalPages,
    currentPage,
    variant = 'default',
    containerStyle,
    dotStyle,
    activeDotStyle,
}) => {
    // 如果只有一页或没有页面，不显示指示器
    if (totalPages <= 1) {
        return null;
    }

    // 根据变体选择样式
    const getVariantStyles = () => {
        switch (variant) {
            case 'card':
                return {
                    container: styles.cardPagination,
                    dot: styles.cardPaginationDot,
                    activeDot: styles.cardPaginationDotActive,
                };
            case 'small':
                return {
                    container: styles.smallPagination,
                    dot: styles.smallPaginationDot,
                    activeDot: styles.smallPaginationDotActive,
                };
            default:
                return {
                    container: styles.pagination,
                    dot: styles.paginationDot,
                    activeDot: styles.paginationDotActive,
                };
        }
    };

    const variantStyles = getVariantStyles();

    return (
        <View style={[variantStyles.container, containerStyle]}>
            {Array.from({ length: totalPages }, (_, index) => (
                <View
                    key={index}
                    style={[
                        variantStyles.dot,
                        dotStyle,
                        index === currentPage
                            ? { ...variantStyles.activeDot, ...activeDotStyle }
                            : null,
                    ]}
                />
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    // 默认样式 - 来自 SkillCards 中的 pagination
    pagination: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 8,
    },
    paginationDot: {
        width: 5,
        height: 5,
        borderRadius: 2.5,
        backgroundColor: '#DDD',
        marginHorizontal: 2,
    },
    paginationDotActive: {
        backgroundColor: COLORS.PRIMARY,
        width: 12,
    },

    // 卡片样式 - 来自 SkillCards 中的 cardPagination
    cardPagination: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 12,
    },
    cardPaginationDot: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: '#DDD',
        marginHorizontal: 3,
    },
    cardPaginationDotActive: {
        backgroundColor: COLORS.PRIMARY,
        width: 16,
    },

    // 小尺寸样式 - 适用于 ShortcutEntries
    smallPagination: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 6,
    },
    smallPaginationDot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        backgroundColor: '#DDD',
        marginHorizontal: 2,
    },
    smallPaginationDotActive: {
        backgroundColor: COLORS.PRIMARY,
        width: 10,
    },
});

export default PaginationIndicator;
