import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Image,
    Dimensions,
    ScrollView,
} from '@mrn/react-native';
import React from 'react';

import {
    AccessibilityEnhancement,
    TouchFeedback,
} from './AccessibilityEnhancement';

import { OperationType } from '@/components/AbilitySelector/useToolbar';
import { useHomePageData } from '@/hooks/useHomePageData';
import { useSendMessage } from '@/hooks/useSendMessage';
import useTrace from '@/hooks/useTrace';
import { EntryPointType } from '@/types';
import { Skill } from '@/types/homePageApi';
import useOpenLink from '@/utils/openLink';

const { width: screenWidth } = Dimensions.get('window');
// 计算每个技能项的宽度：(容器宽度 - 左右边距 - 项目之间的间距) / 4
const ITEM_WIDTH = (screenWidth - 24 - 24) / 4; // 24是左右各12的padding，24是3个间距每个8

interface ShortcutItemProps {
    skill: Skill;
    onPress: (skill: Skill) => void;
    disabled?: boolean;
}

/**
 * 快捷入口项组件 - 上部分为技能图片，下部分为技能名称
 */
const ShortcutItem: React.FC<ShortcutItemProps> = ({
    skill,
    onPress,
    disabled = false,
}) => {
    return (
        <TouchableOpacity
            style={[
                styles.shortcutItem,
                disabled && styles.shortcutItemDisabled,
            ]}
            onPress={() => (!disabled ? onPress(skill) : undefined)}
            {...(disabled ? {} : TouchFeedback.getButtonProps())}
            disabled={disabled}
            {...AccessibilityEnhancement.createAccessibilityProps({
                label: skill.content,
                hint: disabled ? '当前不可用' : '点击执行操作',
                role: 'button',
                state: { disabled },
            })}
        >
            {/* 技能图片 */}
            <View style={styles.skillImageContainer}>
                {skill.link ? (
                    <Image
                        source={{ uri: skill.link }}
                        style={styles.skillImage}
                        resizeMode="contain"
                        {...AccessibilityEnhancement.createAccessibilityProps({
                            label: `${skill.content}图标`,
                            role: 'image',
                        })}
                    />
                ) : (
                    <View style={styles.skillImagePlaceholder} />
                )}
            </View>

            {/* 技能名称 */}
            <Text
                style={[
                    styles.shortcutText,
                    disabled && styles.shortcutTextDisabled,
                ]}
                numberOfLines={2}
            >
                {skill.content}
            </Text>

            {/* 新功能标识 */}
            {skill.isNew && !disabled ? (
                <View
                    style={styles.newBadge}
                    {...AccessibilityEnhancement.createAccessibilityProps({
                        label: '新功能标识',
                        role: 'text',
                    })}
                >
                    <Text style={styles.newBadgeText}>新</Text>
                </View>
            ) : null}
        </TouchableOpacity>
    );
};

export const useSkillClick = (entryPoint = 'homepage_skill_group') => {
    const trace = useTrace();
    const openLink = useOpenLink();
    const { send } = useSendMessage();

    const traceSkillClick = (item: any) => {
        trace(entryPoint, 'trigger', JSON.stringify(item));
    };
    const onSkillClick = (item: any) => {
        if (item.operationType === OperationType.JUMP) {
            openLink(item.url);
        } else if (item.operationType === OperationType.SEND) {
            send(
                { content: item.content, abilityType: item.abilityType },
                EntryPointType.TOOL,
            );
        }
        traceSkillClick(item);
    };
    return onSkillClick;
};
/**
 * 快捷入口组件
 * 保留快捷入口区（如驳回查询、点评离分查询、到手价计算、短信查询）
 * 数据与动作由后端下发，与技能项动作协议统一
 */
const ShortcutEntries: React.FC = () => {
    const { skillGroups, isLoading } = useHomePageData();
    const onSkillClick = useSkillClick();

    // 从skillGroups中提取快捷入口
    // 根据实际后端约定，这里假设有一个特定的快捷入口分组
    const shortcutSkills = React.useMemo(() => {
        const shortcutGroup = skillGroups.find(
            (group) => group.type === 'tiled',
        );
        return shortcutGroup?.skills || [];
    }, [skillGroups]);

    if (isLoading) {
        return null;
    }

    if (shortcutSkills.length === 0) {
        return null;
    }

    return (
        <View style={styles.container}>
            <ScrollView
                style={styles.shortcutList}
                horizontal
                showsHorizontalScrollIndicator={false}
            >
                {shortcutSkills.map((skill, index) => {
                    // 根据业务逻辑判断是否禁用
                    // 这里可以根据skill的某些字段来判断禁用状态
                    const disabled = false; // TODO: 根据实际业务逻辑设置

                    return (
                        <ShortcutItem
                            key={`shortcut-${index}`}
                            skill={skill}
                            onPress={() => onSkillClick(skill)}
                            disabled={disabled}
                        />
                    );
                })}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#FFFFFF',
        paddingTop: 16,
        paddingBottom: 19,
        paddingHorizontal: 12,
        borderRadius: 12,
        marginHorizontal: 12,
        // marginVertical: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    loadingContainer: {
        padding: 16,
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 12,
        color: '#999',
    },
    sectionTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 10,
    },
    shortcutList: {
        flexDirection: 'row',
    },
    shortcutItem: {
        backgroundColor: 'transparent',
        justifyContent: 'flex-start',
        alignItems: 'center',
        position: 'relative',
        flexDirection: 'column',
        width: ITEM_WIDTH,
    },
    shortcutItemDisabled: {
        backgroundColor: '#F5F5F5',
        borderColor: '#E0E0E0',
        opacity: 0.5,
    },
    shortcutText: {
        fontSize: 12,
        color: '#666',
        fontWeight: '500',
        textAlign: 'center',
        lineHeight: 14,
    },
    shortcutTextDisabled: {
        color: '#999',
    },
    newBadge: {
        position: 'absolute',
        top: -2,
        right: -2,
        backgroundColor: '#FF6A00',
        borderRadius: 6,
        paddingHorizontal: 3,
        paddingVertical: 1,
        minWidth: 12,
        height: 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBadgeText: {
        fontSize: 8,
        color: '#FFFFFF',
        fontWeight: '600',
    },
    skillImageContainer: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    skillImage: {
        width: 32,
        height: 32,
    },
    skillImagePlaceholder: {
        width: 32,
        height: 32,
        backgroundColor: '#F0F0F0',
        borderRadius: 16,
    },
});

export default ShortcutEntries;
