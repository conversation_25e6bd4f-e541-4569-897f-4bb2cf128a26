import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    Dimensions,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon } from '@roo/roo-rn';
import React, { useState, useMemo, useEffect, useRef } from 'react';

import {
    AccessibilityEnhancement,
    TouchFeedback,
} from './AccessibilityEnhancement';
import PaginationIndicator from './PaginationIndicator';
import SubSkillPanel from './SubSkillPanel';

import RNImage from '@/components/RNImage';
import { COLORS } from '@/consts';
import { useHomePageData } from '@/hooks/useHomePageData';
import { useLayout } from '@/hooks/useLayout';
import { useSendMessage } from '@/hooks/useSendMessage';
import { useUiState } from '@/store/uiState';
import { EntryPointType } from '@/types';
import { Skill, SkillGroup } from '@/types/homePageApi';

const { width: screenWidth } = Dimensions.get('window');
const CARD_MARGIN = 12;
const CARD_WIDTH = (screenWidth - CARD_MARGIN * 3) / 2; // 一行显示2个卡片

interface SkillItemProps {
    skill: Skill;
    onPress: (
        skill: Skill,
        position?: { x: number; y: number; width: number; height: number },
    ) => void;
    isTiled?: boolean; // 是否为平铺布局
    style?: any;
}

/**
 * 单个技能项组件 - 上部分为图片，下部分为技能名称
 */
const SkillItem: React.FC<SkillItemProps> = ({
    skill,
    onPress,
    style = {},
    isTiled = false,
}) => {
    const buttonRef = useRef<TouchableOpacity>(null);

    const handlePress = () => {
        if (
            buttonRef.current &&
            skill.subSkillList &&
            skill.subSkillList.length > 0
        ) {
            // 如果有子技能，获取位置信息
            buttonRef.current.measureInWindow((x, y, width, height) => {
                onPress(skill, { x, y, width, height });
            });
        } else {
            // 没有子技能，直接调用
            onPress(skill);
        }
    };

    return (
        <TouchableOpacity
            ref={buttonRef}
            style={[styles.skillItem, style]}
            onPress={handlePress}
            {...TouchFeedback.getButtonProps()}
            {...AccessibilityEnhancement.createAccessibilityProps({
                label: skill.content,
                hint: skill.isNew ? '新功能，点击了解详情' : '点击了解详情',
                role: 'button',
            })}
        >
            {/* 技能图片 */}
            <View
                style={
                    isTiled
                        ? styles.tiledSkillImageContainer
                        : styles.skillImageContainer
                }
            >
                <RNImage
                    source={{ uri: skill.link }}
                    style={isTiled ? styles.tiledSkillImage : styles.skillImage}
                    resizeMode="contain"
                    {...AccessibilityEnhancement.createAccessibilityProps({
                        label: `${skill.content}图标`,
                        role: 'image',
                    })}
                />
            </View>

            {/* 技能名称 */}
            <View style={styles.skillNameContainer}>
                <Text
                    style={isTiled ? styles.tiledSkillText : styles.skillText}
                >
                    {skill.content}
                </Text>
                {skill.subSkillList && skill.subSkillList.length > 0 && (
                    <Icon
                        type="right"
                        size={8}
                        tintColor={COLORS.TEXT_TERTIARY}
                        style={{ marginLeft: 2 }}
                    />
                )}
            </View>

            {/* 新功能标识 */}
            {skill.isNew ? (
                <View
                    style={styles.newBadge}
                    {...AccessibilityEnhancement.createAccessibilityProps({
                        label: '新功能标识',
                        role: 'text',
                    })}
                >
                    <Text style={styles.newBadgeText}>新</Text>
                </View>
            ) : null}
        </TouchableOpacity>
    );
};

interface SkillCardProps {
    skillGroup: SkillGroup;
    onSkillPress: (skill: Skill) => void;
    cardIndex: number; // 添加卡片索引，用于渐变背景
    isFirstCard?: boolean; // 是否是第一个卡片
}

/**
 * 技能分类卡片组件
 * 每张卡片固定显示4项技能，超出项用横向滑动分页+指示点
 */
const SkillCard: React.FC<SkillCardProps> = ({
    skillGroup,
    onSkillPress,
    cardIndex,
    isFirstCard = false,
}) => {
    const [currentPage, setCurrentPage] = useState(0);
    const { setSkillCardPosition } = useUiState();
    const { onLayout, layout } = useLayout();

    // 将技能按4个一组分页
    const pages = useMemo(() => {
        const skillsPerPage = 4;
        const pagesArray: Skill[][] = [];
        for (let i = 0; i < skillGroup.skills.length; i += skillsPerPage) {
            pagesArray.push(skillGroup.skills.slice(i, i + skillsPerPage));
        }
        return pagesArray;
    }, [skillGroup.skills]);

    // 根据卡片索引设置渐变背景颜色
    const getGradientColors = () => {
        const colorSets = [
            [COLORS.GRADIENT_ORANGE_START, COLORS.GRADIENT_WHITE_END], // 第一个卡片：橙色渐变
            [COLORS.GRADIENT_PURPLE_START, COLORS.GRADIENT_WHITE_END], // 第二个卡片：紫色渐变
        ];
        return (
            colorSets[cardIndex % 2] || [
                COLORS.BACKGROUND_WHITE,
                COLORS.BACKGROUND_WHITE,
            ]
        );
    };

    const handleScroll = (event: any) => {
        const scrollX = event.nativeEvent.contentOffset.x;
        const page = Math.round(scrollX / CARD_WIDTH);
        setCurrentPage(page);
    };

    useEffect(() => {
        if (isFirstCard) {
            setSkillCardPosition(layout);
        }
    }, [layout]);

    if (skillGroup.skills.length === 0) {
        return null;
    }

    return (
        <LinearGradient
            colors={getGradientColors()}
            start={{ x: 0, y: 0 }}
            end={{ x: 0.5, y: 0.3 }}
            angle={166}
            style={styles.skillCard}
            onLayout={onLayout}
        >
            {/* 分组标题 - 支持艺术字图片或文字显示 */}
            {skillGroup.name || skillGroup.link ? (
                <View style={styles.groupTitleContainer}>
                    {skillGroup.link ? (
                        <RNImage
                            source={{ uri: skillGroup.link }}
                            style={styles.groupTitleImage}
                            resizeMode="contain"
                            {...AccessibilityEnhancement.createAccessibilityProps(
                                {
                                    label: skillGroup.name || '技能分组',
                                    role: 'image',
                                },
                            )}
                        />
                    ) : (
                        <Text style={styles.groupTitle}>{skillGroup.name}</Text>
                    )}
                </View>
            ) : null}

            {/* 技能列表 - 横向滑动 */}
            <ScrollView
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onScroll={handleScroll}
                scrollEventThrottle={16}
                style={styles.skillScrollView}
            >
                {pages.map((pageSkills, pageIndex) => (
                    <View key={pageIndex} style={styles.skillPage}>
                        <View style={styles.skillGrid}>
                            {pageSkills.map((skill, skillIndex) => (
                                <SkillItem
                                    key={`${pageIndex}-${skillIndex}`}
                                    skill={skill}
                                    onPress={onSkillPress}
                                />
                            ))}
                        </View>
                    </View>
                ))}
            </ScrollView>

            {/* 分页指示点 */}
            <PaginationIndicator
                totalPages={pages.length}
                currentPage={currentPage}
                variant="default"
                containerStyle={{ marginTop: -6 }}
            />
        </LinearGradient>
    );
};

interface SkillCardsProps {
    wrapWithTooltip?: (
        key: string,
        children: React.ReactNode,
    ) => React.ReactNode;
}

/**
 * 技能分类卡片列表组件 - 只处理type为group的技能
 */
const SkillCards: React.FC<SkillCardsProps> = ({ wrapWithTooltip }) => {
    const { skillGroups, isLoading } = useHomePageData();
    const { send } = useSendMessage();
    const [currentPage, setCurrentPage] = useState(0);

    // 子技能面板状态
    const [subSkillPanel, setSubSkillPanel] = useState<{
        visible: boolean;
        parentSkill: Skill | null;
        subSkills: Skill[];
        parentSkillPosition?: {
            x: number;
            y: number;
            width: number;
            height: number;
        };
    }>({
        visible: false,
        parentSkill: null,
        subSkills: [],
        parentSkillPosition: undefined,
    });

    // 平铺布局的滚动处理
    const handleTiledScroll = (event: any) => {
        const scrollX = event.nativeEvent.contentOffset.x;
        const pageWidth = screenWidth - 32; // 减去左右边距
        const page = Math.round(scrollX / pageWidth);
        setCurrentPage(page);
    };

    // 处理技能点击
    const handleSkillPress = (
        skill: Skill,
        position?: { x: number; y: number; width: number; height: number },
    ) => {
        // 检查是否有子技能
        if (skill.subSkillList && skill.subSkillList.length > 0) {
            // 有子技能，显示面板
            setSubSkillPanel({
                visible: true,
                parentSkill: skill,
                subSkills: skill.subSkillList,
                parentSkillPosition: position,
            });
        } else {
            // 没有子技能，直接发送问题
            send(skill.content, EntryPointType.SKILL_CARD);
        }
    };

    // 关闭子技能面板
    const handleCloseSubSkillPanel = () => {
        setSubSkillPanel({
            visible: false,
            parentSkill: null,
            subSkills: [],
            parentSkillPosition: undefined,
        });
    };

    // 处理子技能点击
    const handleSubSkillPress = (skill: Skill) => {
        // 子技能点击直接发送问题
        send(skill.content, EntryPointType.SKILL_CARD);
        // 关闭面板
        handleCloseSubSkillPanel();
    };

    // 只处理type为group的技能分组
    const groupSkillGroups = useMemo(() => {
        return skillGroups.filter((group) => group.type === 'group');
    }, [skillGroups]);

    // 检查是否只有一个group技能分组
    const shouldShowTiledLayout = useMemo(() => {
        return groupSkillGroups.length === 1;
    }, [groupSkillGroups]);

    // 将技能卡片按2个一组分页
    const cardPages = useMemo(() => {
        const pages: SkillGroup[][] = [];
        for (let i = 0; i < groupSkillGroups.length; i += 2) {
            pages.push(groupSkillGroups.slice(i, i + 2));
        }
        return pages;
    }, [groupSkillGroups]);

    // 平铺布局的分页逻辑：将技能按4个一组分页
    const tiledPages = useMemo(() => {
        if (groupSkillGroups.length === 0) {
            return [];
        }

        const skillGroup = groupSkillGroups[0];
        const skills = skillGroup.skills;
        const skillsPerPage = 4;
        const pagesArray: Skill[][] = [];

        for (let i = 0; i < skills.length; i += skillsPerPage) {
            pagesArray.push(skills.slice(i, i + skillsPerPage));
        }
        return pagesArray;
    }, [groupSkillGroups]);

    if (isLoading) {
        return null;
    }

    if (groupSkillGroups.length === 0) {
        return null;
    }

    // 平铺布局渲染函数
    const renderTiledLayout = () => {
        const skillGroup = groupSkillGroups[0];

        return (
            <LinearGradient
                colors={[
                    COLORS.GRADIENT_ORANGE_START,
                    COLORS.GRADIENT_WHITE_END,
                ]} // 使用和卡片一样的渐变色
                start={{ x: 0, y: 0 }}
                end={{ x: 0.5, y: 0.3 }}
                angle={166}
                style={styles.tiledContainer}
            >
                {/* 分组标题 */}
                {skillGroup.name || skillGroup.link ? (
                    <View style={styles.tiledGroupTitleContainer}>
                        {skillGroup.link ? (
                            <RNImage
                                source={{ uri: skillGroup.link }}
                                style={styles.tiledGroupTitleImage}
                                resizeMode="contain"
                                {...AccessibilityEnhancement.createAccessibilityProps(
                                    {
                                        label: skillGroup.name || '技能分组',
                                        role: 'image',
                                    },
                                )}
                            />
                        ) : (
                            <Text style={styles.tiledGroupTitle}>
                                {skillGroup.name}
                            </Text>
                        )}
                    </View>
                ) : null}

                {/* 分页技能网格 */}
                <ScrollView
                    horizontal
                    pagingEnabled
                    showsHorizontalScrollIndicator={false}
                    onScroll={handleTiledScroll}
                    scrollEventThrottle={16}
                    style={styles.tiledScrollView}
                >
                    {tiledPages.map((pageSkills, pageIndex) => (
                        <View key={pageIndex} style={styles.tiledSkillGrid}>
                            {pageSkills.map((skill, skillIndex) => (
                                <View
                                    key={`tiled-${pageIndex}-${skillIndex}`}
                                    style={[styles.tiledSkillWrapper]}
                                >
                                    <SkillItem
                                        skill={skill}
                                        onPress={handleSkillPress}
                                        isTiled={true}
                                        style={{ width: '100%' }}
                                    />
                                </View>
                            ))}
                        </View>
                    ))}
                </ScrollView>
                {/* 平铺布局分页指示器 */}
                <PaginationIndicator
                    totalPages={tiledPages.length}
                    currentPage={currentPage}
                    variant="default"
                />
            </LinearGradient>
        );
    };

    // 如果只有一个group技能分组，使用平铺展示
    if (shouldShowTiledLayout) {
        return (
            <View style={styles.container}>
                {renderTiledLayout()}

                {/* 子技能展开面板 */}
                {subSkillPanel.visible && subSkillPanel.parentSkill && (
                    <SubSkillPanel
                        visible={subSkillPanel.visible}
                        parentSkill={subSkillPanel.parentSkill}
                        subSkills={subSkillPanel.subSkills}
                        onClose={handleCloseSubSkillPanel}
                        onSubSkillPress={handleSubSkillPress}
                        parentSkillPosition={subSkillPanel.parentSkillPosition}
                    />
                )}
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.cardScrollView}>
                {cardPages.map((pageCards, pageIndex) => (
                    <View key={pageIndex} style={styles.cardPage}>
                        <View style={styles.cardRow}>
                            {pageCards.map((skillGroup, cardIndex) => {
                                const isFirstCard =
                                    pageIndex === 0 && cardIndex === 0;
                                const isSecondCard =
                                    pageIndex === 0 && cardIndex === 1;
                                const skillCard = (
                                    <SkillCard
                                        skillGroup={skillGroup}
                                        onSkillPress={handleSkillPress}
                                        cardIndex={pageIndex * 2 + cardIndex}
                                        isFirstCard={isFirstCard}
                                    />
                                );

                                // 包装引导提示
                                let wrappedCard: any = skillCard;
                                if (wrapWithTooltip) {
                                    if (isFirstCard) {
                                        wrappedCard = wrapWithTooltip(
                                            'skillCard1',
                                            skillCard,
                                        );
                                    } else if (isSecondCard) {
                                        wrappedCard = wrapWithTooltip(
                                            'skillCard2',
                                            skillCard,
                                        );
                                    }
                                }

                                return (
                                    <View
                                        key={`${skillGroup.type}-${pageIndex}-${cardIndex}`}
                                        style={[
                                            styles.cardWrapper,
                                            isFirstCard || isSecondCard
                                                ? { zIndex: 999 }
                                                : { zIndex: 999 },
                                        ]}
                                    >
                                        {wrappedCard}
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                ))}
            </View>

            {/* 分页指示点 */}
            <PaginationIndicator
                totalPages={cardPages.length}
                currentPage={currentPage}
                variant="card"
            />

            {/* 子技能展开面板 */}
            {subSkillPanel.visible && subSkillPanel.parentSkill && (
                <SubSkillPanel
                    visible={subSkillPanel.visible}
                    parentSkill={subSkillPanel.parentSkill}
                    subSkills={subSkillPanel.subSkills}
                    onClose={handleCloseSubSkillPanel}
                    onSubSkillPress={handleSubSkillPress}
                    parentSkillPosition={subSkillPanel.parentSkillPosition}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingBottom: 16,
    },
    cardScrollView: {
        marginHorizontal: CARD_MARGIN,
    },
    cardPage: {
        width: screenWidth - CARD_MARGIN * 2,
    },
    cardRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    cardWrapper: {
        width: CARD_WIDTH,
    },

    loadingContainer: {
        padding: 16,
        alignItems: 'center',
    },
    loadingText: {
        fontSize: 13,
        color: COLORS.TEXT_TERTIARY,
    },
    skillCard: {
        borderRadius: 12,
        paddingTop: 16,
        paddingHorizontal: 16,
        paddingBottom: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
        flex: 1,
        minHeight: 140,
        borderColor: COLORS.BORDER_WHITE,
        borderWidth: 2,
    },
    groupTitleContainer: {
        marginBottom: 10,
        alignItems: 'flex-start',
    },
    groupTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: COLORS.TEXT_PRIMARY,
        textAlign: 'left',
    },
    groupTitleImage: {
        height: 16,
        marginBottom: 4,
    },
    skillScrollView: {
        marginHorizontal: -12,
    },
    skillPage: {
        width: CARD_WIDTH,
        paddingHorizontal: 12,
    },
    skillGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        alignContent: 'flex-start',
        // 当元素不够时居中显示
        minHeight: 80, // 确保有足够的高度
    },
    skillItem: {
        width: '48%',
        backgroundColor: 'transparent',
        marginBottom: 8,
        minHeight: Math.max(
            56,
            TouchFeedback.getButtonProps().activeOpacity ? 44 : 40,
        ),
        justifyContent: 'flex-start',
        alignItems: 'center',
        position: 'relative',
    },
    skillText: {
        fontSize: 12,
        color: COLORS.TEXT_SECONDARY,
        textAlign: 'center',
        lineHeight: 16,
    },
    newBadge: {
        position: 'absolute',
        top: 3,
        right: 3,
        backgroundColor: COLORS.PRIMARY,
        borderRadius: 6,
        paddingHorizontal: 3,
        paddingVertical: 1,
        minWidth: 14,
        height: 14,
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBadgeText: {
        fontSize: 9,
        color: COLORS.BACKGROUND_WHITE,
        fontWeight: '600',
    },
    skillImageContainer: {
        width: 32,
        height: 32,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 4,
    },
    skillImage: {
        width: 28,
        height: 28,
    },
    skillNameContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },

    // 平铺布局样式
    tiledContainer: {
        marginHorizontal: CARD_MARGIN,
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
        borderColor: COLORS.BORDER_WHITE,
        borderWidth: 2,
    },
    tiledGroupTitleContainer: {
        marginBottom: 16,
        alignItems: 'flex-start',
    },
    tiledGroupTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: COLORS.TEXT_PRIMARY,
        textAlign: 'left',
    },
    tiledGroupTitleImage: {
        height: 16,
        marginTop: 10,
        marginBottom: 4,
    },
    tiledScrollView: {
        flex: 1,
    },
    tiledSkillGrid: {
        width: screenWidth - 32, // 减去左右边距
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        paddingHorizontal: 0,
    },
    tiledSkillWrapper: {
        width: '23%', // 每行4个技能，留出间距
        alignItems: 'center',
    },
    tiledSkillImageContainer: {
        width: 48, // 平铺布局中图标更大
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 6,
    },
    tiledSkillImage: {
        width: 28, // 平铺布局中图标更大
    },
    tiledSkillText: {
        fontSize: 12,
        color: COLORS.TEXT_PRIMARY, // 平铺布局中文字颜色稍深
        textAlign: 'center',
        lineHeight: 16,
        marginTop: 2,
    },
});

export default SkillCards;
