import { StyleSheet, Text, TouchableOpacity, View } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import _ from 'lodash';
import React, { useState } from 'react';

import AnswerContent from './AnswerContent';
import { AnswerContext } from './AnswerContext';
import AnswerFooter from './AnswerFooter';
import useOptionPress from '../../../hooks/useOptionPress';
import { Message, MessageContentType } from '../../../types';
import { SuffixOptionsMessage } from '../../../types/message';
import getKeyFromArray from '../../../utils/getKeyFromArray';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import RNImage from '@/components/RNImage';

const styles = StyleSheet.create({
    container: {
        marginBottom: 12,
    },
    messageContainer: {
        backgroundColor: '#fff',
        borderRadius: 10.5,
        paddingHorizontal: 16,
        paddingTop: 12,
        paddingBottom: 8,
        overflow: 'hidden',
    },
    text: {
        fontSize: 14,
        color: '#fff',
    },
    gradientOverlay: {
        position: 'absolute',
        height: 76,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
});

interface Answer {
    data: Message;
}

const EXTRA_STYLE = {
    [MessageContentType.WELCOME]: { flex: 1 },
    [MessageContentType.WITH_OPTIONS]: { minWidth: 230 },
};

const Answer = (props: Answer) => {
    const extraStyle = EXTRA_STYLE[props.data.msgType];
    const [typed, setTyped] = useState([]);
    const { onOptionPress } = useOptionPress(
        _.pick(props.data, ['msgType', 'msgId', 'history']),
    );
    const [withForm, setWithForm] = useState(false);

    // 检查消息是否只包含hideSpan类型内容，如果是则不展示
    const shouldHideMessage = () => {
        const currentContent = props.data.currentContent;
        if (!currentContent || !Array.isArray(currentContent)) {
            return false;
        }

        // 过滤掉hideSpan类型的消息，检查是否还有其他有效内容
        const validContent = currentContent.filter(
            (item) => item && item.type && item.type !== 'hideSpan',
        );

        // 如果没有有效内容，则隐藏整个消息
        return validContent.length === 0;
    };

    // 如果消息只包含hideSpan内容，则不渲染
    if (shouldHideMessage()) {
        return null;
    }
    return (
        <AnswerContext.Provider value={{ withForm, setWithForm }}>
            <View style={styles.container}>
                <View style={extraStyle}>
                    <View style={[styles.messageContainer]}>
                        <Condition condition={[withForm]}>
                            <LinearGradient
                                colors={['#FAF8FF', '#FFFFFF']}
                                locations={[0, 0.2]}
                                style={styles.gradientOverlay}
                            />
                        </Condition>
                        {/* <Condition condition={[withForm]}>
                            <LinearGradient
                                colors={['#FFFFFF00', '#FFFFFF']}
                                locations={[0, 0.68]}
                                style={styles.gradientOverlay}
                            />
                        </Condition> */}

                        <View style={{ position: 'relative' }}>
                            <AnswerContent
                                {...props}
                                onTypedChange={setTyped}
                            />

                            {/*Footer，展示点赞按钮、会话状态等*/}
                            <AnswerFooter {...props} />
                        </View>
                    </View>
                </View>

                {/*猜你想问，因和整个消息体的UI在同一层级，所以需要提升进行渲染*/}
                {typed
                    .filter((v) => v?.type === 'suffixOptions')
                    .map((v: SuffixOptionsMessage) => {
                        const { descriptions, options } =
                            v.insert.suffixOptions;
                        return (
                            <View style={{ marginTop: 8 }}>
                                <Condition condition={[descriptions]}>
                                    <Text
                                        style={{
                                            marginBottom: 8,
                                            color: '#3d3d3d',
                                            fontSize: 12,
                                        }}
                                    >
                                        {descriptions}
                                    </Text>
                                </Condition>

                                <View>
                                    {options.map((op) => {
                                        return (
                                            <TouchableOpacity
                                                key={getKeyFromArray([
                                                    'suffixOptionItem',
                                                    op.content,
                                                ])}
                                                style={{
                                                    backgroundColor: '#FAFAFB',
                                                    borderRadius: 12,
                                                    padding: 10,
                                                    marginBottom: 12,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    alignSelf: 'flex-start',
                                                }}
                                                onPress={() =>
                                                    onOptionPress(
                                                        op,
                                                        'floating_option_list',
                                                    )
                                                }
                                            >
                                                <Text
                                                    style={{
                                                        color: '#222',
                                                        fontSize: 12,
                                                    }}
                                                >
                                                    {op.content}
                                                </Text>
                                                <RNImage
                                                    source={
                                                        NetImages.arrowRight
                                                    }
                                                    style={{
                                                        width: 10,
                                                        marginLeft: 4,
                                                    }}
                                                />
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                            </View>
                        );
                    })}
            </View>
        </AnswerContext.Provider>
    );
};

export default Answer;
