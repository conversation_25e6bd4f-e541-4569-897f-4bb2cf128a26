import {
    StyleSheet,
    Text,
    View,
    Image,
    TouchableOpacity
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';
import Svg, {
    Defs,
    LinearGradient,
    Stop,
    Text as SvgText
} from 'react-native-svg';
import LinearGradientRN from 'react-native-linear-gradient';

import NetImage from '../../assets/images/homeRefactor';
import TWS from '../../TWS';
import Condition from '../Condition/Condition';

import { TaskJob } from '@/api/taskApi';

interface AICallItemProps {
    item: TaskJob;
    onViewResult?: (job: TaskJob) => void;
}

// 紫色渐变文字组件
const GradientText: React.FC<{ text: string }> = ({ text }) => {
    return (
        <Svg height="16" width={text.length * 12}>
            <Defs>
                <LinearGradient
                    id="purpleGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                >
                    <Stop offset="0%" stopColor="#4021FF" stopOpacity="1" />
                    <Stop offset="100%" stopColor="#7F6CF4" stopOpacity="1" />
                </LinearGradient>
            </Defs>
            <SvgText
                fill="url(#purpleGradient)"
                fontSize="12"
                fontWeight="400"
                x="0"
                y="12"
            >
                {text}
            </SvgText>
        </Svg>
    );
};

const AICallItem: React.FC<AICallItemProps> = ({ item, onViewResult }) => {
    // 匹配PC端逻辑：从itemList[0]获取状态信息
    const firstItem = item.itemList?.[0];
    const statusText = firstItem?.statusText || '';
    const statusTextColor = firstItem?.statusTextColor;
    const isRunningStyle = statusTextColor === '#running';

    // 匹配PC端的canViewResult逻辑
    const canViewResult = firstItem?.operationType == 2 && firstItem?.content;

    const getStatusInfo = () => {
        if (isRunningStyle) {
            return {
                text: statusText,
                style: styles.processingStatusText,
                isRunningStyle: true
            };
        }
        if (['fail', 'failed'].includes(item.status)) {
            return {
                text: statusText,
                style: styles.failedStatusText,
                isRunningStyle: false
            };
        }
        if (['success', 'completed'].includes(item.status)) {
            return {
                text: statusText,
                style: styles.completedStatusText,
                isRunningStyle: false
            };
        }
        // 使用自定义颜色或默认样式
        const customStyle =
            statusTextColor && statusTextColor !== '#running'
                ? { color: statusTextColor }
                : {};
        return { text: statusText, style: customStyle, isRunningStyle: false };
    };

    const statusInfo = getStatusInfo();

    const handlePress = () => {
        if (onViewResult && canViewResult) {
            onViewResult(item);
        }
    };

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={handlePress}
            disabled={!canViewResult}
            activeOpacity={0.7}
        >
                <View style={styles.header}>
                    <Text style={styles.title}>外呼任务</Text>
                    {statusInfo.text ? (
                        statusInfo.isRunningStyle ? (
                            <View style={styles.runningStatusContainer}>
                                <Image
                                    source={{ uri: NetImage.star }}
                                    style={styles.starIcon}
                                />
                                <GradientText text={statusInfo.text} />
                            </View>
                        ) : (
                            <View style={TWS.row()}>
                                <Text
                                    style={[
                                        styles.statusBase,
                                        statusInfo.style
                                    ]}
                                >
                                    {statusInfo.text}
                                </Text>
                                <Condition condition={[canViewResult]}>
                                    <Icon
                                        type={'right'}
                                        size={12}
                                        style={{ marginTop: 1.5 }}
                                    />
                                </Condition>
                            </View>
                        )
                    ) : null}
                </View>
                <View style={styles.content}>
                    <Text style={styles.detailText}>
                        任务名称：{item.jobName}
                    </Text>
                    <Text style={styles.detailText}>
                        外呼商家：{item.poiNum}家
                    </Text>
                    {item.agentName && (
                        <Text style={styles.detailText}>
                            外呼agent：{item.agentName}
                        </Text>
                    )}
                </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        padding: 12,
        marginRight: 12,
        marginBottom: 12,
        borderColor: '#fff',
        borderWidth: 2,
        borderBottomWidth: 0
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8
    },
    title: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222222'
    },
    content: {
        paddingLeft: 4
    },
    detailText: {
        fontSize: 13,
        color: '#666666',
        lineHeight: 20
    },
    statusBase: {
        fontSize: 12,
        fontWeight: '400',
        color: '#86909c',
        marginLeft: 8
    },
    processingStatusText: {
        color: '#666666',
        ...TWS.button({
            borderWidth: 1,
            borderColor: '#E6E6E6',
            paddingHorizontal: 10,
            paddingVertical: 4,
            borderRadius: 12
        })
    },
    completedStatusText: {
        color: '#52C41A'
    },
    failedStatusText: {
        color: '#FF4D4F'
    },
    runningStatusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 8
    },
    starIcon: {
        width: 12,
        height: 12,
        marginRight: 4
    }
});

export default AICallItem;
