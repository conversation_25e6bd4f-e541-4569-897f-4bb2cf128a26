import { Platform, Dimensions } from '@mrn/react-native';
import { useMemo } from 'react';

import useKeyboard from './useKeyboard';

import { useUiState } from '@/store/uiState';

const { height: screenHeight } = Dimensions.get('window');

// ActionPanel 的估算高度
const ACTION_PANEL_HEIGHT = 120; // padding(20*2) + 图标高度(约80)

/**
 * 计算绝对定位的 hook
 * 根据平台和面板状态自动调整位置
 * 
 * iOS: 键盘不会把UI顶起，所以需要加上keyboardOffset
 * Android: 键盘会把UI顶起，所以不需要加上keyboardOffset
 * 
 * 当panelOpen时，两个平台都需要加上panel的高度
 */
const useAbsolutePosition = () => {
    const { keyboardOffset } = useKeyboard();
    const { inputPosition, panelOpen } = useUiState();

    const absolutePosition = useMemo(() => {
        // 基础位置：输入框上方10像素
        let bottom = inputPosition.height + 10;

        // 平台特定的键盘偏移处理
        if (Platform.OS === 'ios') {
            // iOS：键盘不会把UI顶起，需要加上键盘偏移
            bottom += keyboardOffset;
        }
        // Android：键盘会把UI顶起，不需要加上键盘偏移

        // 面板打开时，两个平台都需要加上面板高度
        if (panelOpen) {
            bottom += ACTION_PANEL_HEIGHT;
        }

        return { bottom };
    }, [keyboardOffset, inputPosition.height, panelOpen]);

    return absolutePosition;
};

export default useAbsolutePosition;
