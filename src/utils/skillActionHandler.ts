import KNB from '@mrn/mrn-knb';
import { Linking } from '@mrn/react-native';

import { Skill, SkillActionType, SkillActionParams } from '@/types/homePageApi';

/**
 * 技能动作处理器
 * 统一处理技能点击动作，支持发问、跳转、路由等多种交互类型
 */
export class SkillActionHandler {
    /**
     * 根据技能配置确定动作类型
     */
    static determineActionType(skill: Skill): SkillActionType {
        // 优先判断是否有子技能列表
        if (skill.subSkillList && skill.subSkillList.length > 0) {
            return SkillActionType.EXPAND_SUB;
        }

        // 基于 operationType 判断交互类型
        switch (skill.operationType) {
            case 1: // 发起提问类型
                return SkillActionType.ASK_QUESTION;
            case 2: // URL跳转类型
                return SkillActionType.OPEN_URL;
            case 3: // 路由导航类型
                return SkillActionType.NAVIGATE;
            default:
                // 默认行为：有URL则跳转，否则发问
                if (skill.url || skill.link) {
                    return SkillActionType.OPEN_URL;
                }
                return SkillActionType.ASK_QUESTION;
        }
    }

    /**
     * 处理技能点击事件
     */
    static handleSkillClick(
        skill: Skill,
        params: Omit<SkillActionParams, 'skill'>,
    ) {
        const actionType = this.determineActionType(skill);
        this.executeAction(actionType, skill, params);
    }

    /**
     * 执行具体的动作
     */
    static executeAction(
        actionType: SkillActionType,
        skill: Skill,
        params: Omit<SkillActionParams, 'skill'>,
    ) {
        try {
            switch (actionType) {
                case SkillActionType.ASK_QUESTION:
                    if (params.onAsk) {
                        params.onAsk(skill.content);
                    } else {
                        console.warn(
                            'No onAsk handler provided for skill:',
                            skill.content,
                        );
                    }
                    break;

                case SkillActionType.OPEN_URL:
                    this.handleUrlOpen(skill, params.onOpenUrl);
                    break;

                case SkillActionType.NAVIGATE:
                    if (params.onNavigate) {
                        params.onNavigate(skill.url || skill.link);
                    } else {
                        console.warn(
                            'No onNavigate handler provided for skill:',
                            skill.content,
                        );
                    }
                    break;

                case SkillActionType.EXPAND_SUB:
                    if (params.onExpandSub) {
                        params.onExpandSub(skill);
                    } else {
                        console.warn(
                            'No onExpandSub handler provided for skill:',
                            skill.content,
                        );
                    }
                    break;

                default:
                    console.warn('Unknown skill action type:', actionType);
            }
        } catch (error) {
            console.error('Failed to execute skill action:', error);
        }
    }

    /**
     * 处理URL打开逻辑
     */
    private static handleUrlOpen(
        skill: Skill,
        onOpenUrl?: (url: string) => void,
    ) {
        const targetUrl = skill.url || skill.link;

        if (!targetUrl) {
            console.warn('No URL found for skill:', skill.content);
            return;
        }

        if (onOpenUrl) {
            onOpenUrl(targetUrl);
            return;
        }

        // RN端统一使用webview打开URL
        try {
            KNB.use('hybrid.native.webview', {
                url: targetUrl,
                title: skill.content,
            });
        } catch (error) {
            console.error('Failed to open URL in webview:', error);
            // 降级使用系统浏览器
            Linking.openURL(targetUrl).catch((linkError) => {
                console.error('Failed to open URL in browser:', linkError);
            });
        }
    }
}

/**
 * Hook风格的便捷接口
 * 返回处理函数，方便在组件中使用
 */
export const useSkillActionHandler = (
    params: Omit<SkillActionParams, 'skill'>,
) => {
    const handleSkillClick = (skill: Skill) => {
        SkillActionHandler.handleSkillClick(skill, params);
    };

    return {
        handleSkillClick,
        determineActionType: SkillActionHandler.determineActionType,
    };
};

// 默认导出
export default SkillActionHandler;
